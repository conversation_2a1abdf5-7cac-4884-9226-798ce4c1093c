import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/audio_player.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/main_lesson_app_bar.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class SpeakingArenaScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;

  const SpeakingArenaScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<SpeakingArenaScreen> createState() =>
      _SpeakingArenaScreenState();
}

class _SpeakingArenaScreenState extends ConsumerState<SpeakingArenaScreen>
    with TickerProviderStateMixin {
  late AsyncValue<SpeakingState> viewState;
  late SpeakingController viewModel;
  final AudioPlayer _player = AudioPlayer();
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  bool _isAnimationComplete = false;
  String? _currentAudioUrl; // Tracks the current audio URL set to the player

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    )..addStatusListener(_handleAnimationStatus);
    _animationController.forward();
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_isAnimationComplete) {
      _animationController.reverse();
    } else if (status == AnimationStatus.dismissed) {
      setState(() => _isAnimationComplete = true);
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _player.stop();
    _player.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final prov = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.stage1,
    );
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    // Listener for errors
    ref.listen(prov.select((value) => value), ((previous, next) {
      next.maybeWhen(
        error: (error, track) {
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(error.toString())));
          }
        },
        orElse: () {},
      );
    }));

    // Listener for managing audio player source based on state changes
    ref.listen<
      SpeakingState?
    >(prov.select((asyncValue) => asyncValue.valueOrNull), (
      previousState,
      nextState,
    ) async {
      if (!mounted) return;

      String? newAudioUrl;

      if (nextState != null && nextState.speakings.isNotEmpty) {
        final currentSpeaking = nextState.speakings[nextState.selectedIndex];
        newAudioUrl =
            nextState.isQuestionTalking
                ? currentSpeaking.question.audio
                : currentSpeaking.answer.audio;
      }

      if (_currentAudioUrl != newAudioUrl) {
        await _player.stop();
        if (newAudioUrl != null && newAudioUrl.isNotEmpty) {
          _player.setSource(UrlSource(newAudioUrl));
        }
        // Update _currentAudioUrl regardless of whether setSource was called (e.g. newAudioUrl is empty)
        // This ensures _currentAudioUrl correctly reflects the intended audio source (or lack thereof).
        _currentAudioUrl = newAudioUrl;
      }
    });

    return switch (viewState) {
      AsyncData(:final value) => _buildContent(context, value),
      AsyncError(:final error) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('An error occurred: $error'),
          ),
        ),
      ),
      AsyncLoading() => const Scaffold(body: AppLoading()),
      _ => const Scaffold(body: Center(child: Text('Loading...'))),
    };
  }

  Widget _buildContent(BuildContext context, SpeakingState state) {
    if (state.showStageOnboarding == true) {
      return FadeTransition(
        opacity: _animation,
        child:
            _isAnimationComplete
                ? _buildStageListenAll(state)
                : _buildStageTransition(
                  image: '$assetImageMainLesson/speaking_arena/BG27.png',
                  title: 'Stage I',
                  subTitle: context.loc.stage1Speaking,
                ),
      );
    } else {
      return _buildStageListenAll(state);
    }
  }

  Widget _buildStageListenAll(SpeakingState state) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: MainLessonAppBar(
        title: context.loc.speakingArena,
        subtitle: context.loc.stage1SpeakingDesc,
        isBookmarked:
            viewState.value!.speakings.length > 0
                ? viewState
                    .value!
                    .speakings[viewState.value!.selectedIndex]
                    .isBookmarked
                : false,
        onBookmark: () {
          viewModel.saveBookmark();
        },
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFECEC),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            ListView(
              padding: const EdgeInsets.only(
                bottom:
                    120, // Adjusted for NextButton and potentially AudioPlayerWidget
              ),
              children: [
                if (state.questionLength > 0)
                  _buildProgressIndicator(state, size),
                const SizedBox(height: 24.0),
                if (state.speakings.isNotEmpty) _buildQAContainer(state),
                if (state.speakings.isNotEmpty) ...[
                  const SizedBox(
                    height: 24.0,
                  ), // Spacing before AudioPlayerWidget
                  Padding(
                    // Added padding for the AudioPlayerWidget if needed
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: AudioPlayerWidget(
                      player: _player,
                      onComplete: () {
                        // Ensure we use the latest state if possible, or rely on viewmodel
                        // The 'state' here is the one passed to _buildStageListenAll
                        final currentSpeaking =
                            state.speakings[state.selectedIndex];
                        viewModel.setQAActive(
                          session:
                              currentSpeaking.question.isActive
                                  ? SpeakingSessionType.answer
                                  : SpeakingSessionType.question,
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
            // NextButton(onTap: () => viewModel.nextQuestion(context)),
            RepeatNextButton(
              onTapRepeat: () => viewModel.prevQuestion(),
              onTapNext: () => viewModel.nextQuestion(context),
              leftTitle: context.loc.previous,
              leftActive: state.selectedIndex > 0,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(SpeakingState state, Size size) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: size.width - 150,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: LinearProgressIndicator(
                value: (state.selectedIndex + 1) / state.questionLength,
                backgroundColor: const Color(0xffFFDAD2),
                valueColor: AlwaysStoppedAnimation(
                  Theme.of(context).primaryColor,
                ),
                minHeight: 10.0,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xff682000), Color(0xff490206)],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
              borderRadius: const BorderRadius.all(Radius.circular(8.0)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: .15),
                  blurRadius: 6.0,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            width: 98,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Text(
              '${state.selectedIndex + 1} / ${state.questionLength}',
              style: Theme.of(
                context,
              ).textTheme.titleLarge!.copyWith(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQAContainer(SpeakingState state) {
    final currentSpeaking = state.speakings[state.selectedIndex];

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Colors.white.withValues(alpha: .75),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: .12),
            blurRadius: 18.0,
            spreadRadius: -2.0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20.0),
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          _buildQASection(
            prefix: 'Q',
            content: currentSpeaking.question,
            onTap:
                () => viewModel.setQAActive(
                  session: SpeakingSessionType.question,
                ),
            state: state,
          ),
          const SizedBox(height: 16.0),
          _buildQASection(
            prefix: 'A',
            content: currentSpeaking.answer,
            onTap:
                () =>
                    viewModel.setQAActive(session: SpeakingSessionType.answer),
            state: state,
          ),
          // AudioPlayerWidget is removed from here
        ],
      ),
    );
  }

  Widget _buildQASection({
    required String prefix,
    required dynamic content, // Assuming content has 'isActive' and 'text'
    required VoidCallback onTap,
    required SpeakingState state,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.0),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$prefix${state.selectedIndex + 1}: ',
              style:
                  content.isActive
                      ? Theme.of(context).textTheme.titleLarge
                      : Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: const Color(0xff7F7573),
                      ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    content.text,
                    style:
                        content.isActive
                            ? Theme.of(context).textTheme.titleLarge
                            : Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: const Color(0xff7F7573),
                            ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStageTransition({
    required String image,
    required String title,
    required String subTitle,
  }) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xffFFF2F2),
            Color(0xffFDD8D8),
            Color(0xffFFECEC),
            Color(0xffFFFFFF),
          ],
          begin: Alignment.bottomLeft,
          end: Alignment.topRight,
        ),
      ),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(title, style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: 62),
          Container(
            width: 253,
            height: 253,
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [
                  Color(0xffFE754C),
                  Color(0xffE21F29),
                  Color(0xffC3151F),
                ],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
              image: DecorationImage(
                image: AssetImage(image),
                fit: BoxFit.fill,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: .12),
                  blurRadius: 15.0,
                  spreadRadius: 1.0,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24.0),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Text(
              subTitle,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
