import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/library/presentation/providers/library_chapter_controller.dart';
import 'package:selfeng/features/library/presentation/widgets/library_chapter_card.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LibraryChapterScreen extends ConsumerStatefulWidget {
  final String level;

  const LibraryChapterScreen({super.key, required this.level});

  @override
  ConsumerState<LibraryChapterScreen> createState() =>
      _LibraryChapterScreenState();
}

class _LibraryChapterScreenState extends ConsumerState<LibraryChapterScreen> {
  late final Level _levelEnum = Level.values.byName(widget.level.toLowerCase());
  late final _provider = libraryChapterControllerProvider(widget.level);

  @override
  Widget build(BuildContext context) {
    final libraryChapterState = ref.watch(_provider);
    final locale = Localizations.localeOf(context).languageCode;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          '${widget.level.toUpperCase()} - ${libraryChapterState.value?.levelInfo?.title.getByLocale(locale) ?? ''}',
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          textAlign: TextAlign.left,
        ),
        centerTitle: false,
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            // size: 36,
            // color: Colors.black,
          ),
        ),
      ),
      body: libraryChapterState.when(
        data:
            (state) => Column(
              children: [
                SizedBox(height: 42),
                Expanded(
                  child:
                      state.chapters.isEmpty
                          ? const LoadingCircle()
                          : ListView.builder(
                            padding: EdgeInsets.zero,
                            itemCount: state.chapters.length,
                            itemBuilder:
                                (context, index) => LibraryChapterCard(
                                  chapter: state.chapters[index],
                                  level: _levelEnum,
                                ),
                          ),
                ),
              ],
            ),
        loading:
            () => Column(
              children: [
                const SizedBox(height: 80),
                // Show a shimmer effect for the level card
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: .7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                const Expanded(child: Center(child: LoadingCircle())),
              ],
            ),
        error: (error, stack) => Center(child: Text(error.toString())),
      ),
    );
  }
}
