import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/library/domain/models/content_info.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/library/presentation/providers/library_chapter_content_controller.dart';
import 'package:selfeng/features/library/presentation/providers/state/chapter_content_state.dart';
import 'package:selfeng/features/library/presentation/providers/state/library_chapter_content_state.dart';
import 'package:selfeng/features/library/presentation/widgets/content_section_card.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LibraryChapterContentScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;

  const LibraryChapterContentScreen({
    super.key,
    required this.level,
    required this.chapter,
  });

  @override
  ConsumerState<LibraryChapterContentScreen> createState() =>
      _LibraryChapterContentState();
}

class _LibraryChapterContentState
    extends ConsumerState<LibraryChapterContentScreen> {
  List<ContentInfo> _contentSections = [];
  bool _isInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _initializeContentSections();
    }
  }

  void _initializeContentSections() {
    _contentSections = [
      ContentInfo(
        title: context.loc.pronunciationChallenge,
        image: '$assetImageTOC/pronunciationchallenge.png',
        icon: '$assetImageTOC/iconvocabulary.png',
        iconSecondary: '$assetImageTOC/iconexpression.png',
        section: SectionType.pronunciation,
        sectionColor: 0xff7D000B,
      ),
      ContentInfo(
        title: context.loc.conversationVideo,
        image: '$assetImageTOC/conversationvideo.png',
        icon: '$assetImageTOC/iconconversation.png',
        section: SectionType.conversation,
        sectionColor: 0xff93000F,
      ),
      ContentInfo(
        title: context.loc.listeningMastery,
        image: '$assetImageTOC/listeningmastery.png',
        icon: '$assetImageTOC/iconlistening.png',
        section: SectionType.listening,
        sectionColor: 0xffA90013,
      ),
      ContentInfo(
        title: context.loc.speakingArena,
        image: '$assetImageTOC/speakingarena.png',
        icon: '$assetImageTOC/iconspeakingstage1.png',
        iconSecondary: '$assetImageTOC/iconspeaking.png',
        section: SectionType.speaking,
        sectionColor: 0xffC00017,
      ),
    ];

    _isInitialized = true;
  }

  @override
  Widget build(BuildContext context) {
    final contentProvider = libraryChapterContentControllerProvider(
      widget.level,
      widget.chapter,
    );
    final contentState = ref.watch(contentProvider);

    final ChapterContentState content = ref.watch(chapterContentStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.loc.skills_list,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          textAlign: TextAlign.left,
        ),
        centerTitle: false,
        backgroundColor: Colors.white,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            // size: 36,
            // color: Colors.black,
          ),
        ),
      ),
      body: contentState.when(
        data: (state) => _buildContent(state, content),
        loading: () => const Center(child: LoadingCircle()),
        error: (error, stackTrace) => Center(child: Text(error.toString())),
      ),
    );
  }

  Widget _buildContent(
    LibraryChapterContentState state,
    ChapterContentState content,
  ) {
    return _contentSections.isEmpty
        ? const Center(child: Text("No items available"))
        : _buildContentSectionsList(state, content);
  }

  Widget _buildContentSectionsList(
    LibraryChapterContentState state,
    ChapterContentState content,
  ) {
    return ListView.builder(
      itemCount: _contentSections.length,
      itemBuilder: (context, index) {
        final section = _contentSections[index];
        List<ContentIndexData>? sectionContent;

        switch (section.section) {
          case SectionType.pronunciation:
            sectionContent = content.pronunciationContent;
            break;
          case SectionType.conversation:
            sectionContent = content.conversationContent;
            break;
          case SectionType.listening:
            sectionContent = content.listeningContent;
            break;
          case SectionType.speaking:
            sectionContent = content.speakingContent;
            break;
        }

        return ContentSectionCard(
          contentInfo: section,
          content: sectionContent,
          level: widget.level,
          chapter: widget.chapter,
        );
      },
    );
  }
}
