import 'package:flutter/material.dart';
import 'package:selfeng/features/library/domain/models/content_info.dart';
import 'package:selfeng/features/library/presentation/widgets/content_tree.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

class ContentSectionCard extends StatefulWidget {
  final ContentInfo contentInfo;
  final List<ContentIndexData>? content;
  final String level;
  final String chapter;

  const ContentSectionCard({
    super.key,
    required this.contentInfo,
    required this.content,
    required this.level,
    required this.chapter,
  });

  @override
  State<ContentSectionCard> createState() => _ContentSectionCardState();
}

class _ContentSectionCardState extends State<ContentSectionCard> {
  bool _isExpanded = false;

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [_buildHeader(), if (_isExpanded) _buildExpandedContent()],
      ),
    );
  }

  Widget _buildHeader() {
    return GestureDetector(
      onTap: _toggleExpansion,
      child: Container(
        height: 80,
        color: Color(widget.contentInfo.sectionColor),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Image.asset(
                      widget.contentInfo.image,
                      width: 32,
                      height: 32,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.image_not_supported,
                          color: Colors.white,
                        );
                      },
                    ),
                    const SizedBox(width: 16),
                    Flexible(
                      child: Text(
                        widget.contentInfo.title,
                        style: Theme.of(
                          context,
                        ).textTheme.titleLarge?.copyWith(color: Colors.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                color: Colors.white,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedContent() {
    return Container(
      width: double.infinity,
      color: Color(0xffFFF8F7),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          if (widget.content != null)
            ContentTree(
              content: widget.content!,
              iconPath: widget.contentInfo.icon,
              iconPathSecondary: widget.contentInfo.iconSecondary,
              level: widget.level,
              chapter: widget.chapter,
              section: widget.contentInfo.section.name,
            ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
