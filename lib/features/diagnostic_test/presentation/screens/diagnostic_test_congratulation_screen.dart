import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/diagnostic_test/presentation/providers/diagnostic_test_controller.dart';
import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class DiagnosticTestCongratulationScreen extends ConsumerStatefulWidget {
  const DiagnosticTestCongratulationScreen({super.key});

  @override
  ConsumerState<DiagnosticTestCongratulationScreen> createState() =>
      _DiagnosticTestCongratulationScreenState();
}

class _DiagnosticTestCongratulationScreenState
    extends ConsumerState<DiagnosticTestCongratulationScreen>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late AsyncValue viewState;
  late DiagnosticTestController viewModel;
  late ScrollController _scrollController;
  late Size _size;
  int _index = 0;
  final activePainter = Paint();
  final inactivePainter = Paint();

  late List<DefaultModel> _listImage;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    activePainter.color = Colors.red;
    activePainter.strokeWidth = 1;
    activePainter.strokeCap = StrokeCap.round;
    activePainter.style = PaintingStyle.fill;

    inactivePainter.color = const Color.fromARGB(255, 212, 212, 212);
    inactivePainter.strokeWidth = 1;
    inactivePainter.strokeCap = StrokeCap.round;
    inactivePainter.style = PaintingStyle.fill;

    _tabController = TabController(length: 3, vsync: this);
    _index = 0;

    _tabController.addListener(() {
      setState(() {
        _index = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    viewState = ref.watch(diagnosticTestControllerProvider);
    viewModel = ref.watch(diagnosticTestControllerProvider.notifier);
    _size = MediaQuery.of(context).size;
    _listImage = [
      DefaultModel(
        title: context.loc.onboarding1,
        image: '$assetImageDiagnosticTest/BG (1).png',
      ),
      DefaultModel(
        title: context.loc.onboarding2,
        image: '$assetImageDiagnosticTest/BG (2).png',
      ),
      DefaultModel(
        title: context.loc.onboarding3,
        image: '$assetImageDiagnosticTest/BG (3).png',
      ),
    ];

    ref.listen(diagnosticTestControllerProvider.select((value) => value), ((
      previous,
      next,
    ) {
      next.maybeWhen(
        error: (error, track) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    }));

    return Scaffold(
      body: Stack(
        children: [
          TabBarView(
            controller: _tabController,
            children: _listImage.map<Widget>((item) => page(item)).toList(),
          ),
          Positioned(
            bottom: 32,
            left: 0,
            right: 0,
            child: Column(
              children: [
                SizedBox(
                  width: _size.width,
                  height: 8,
                  child: Center(
                    child: SizedBox(
                      width: 80, // Fixed width for the dots container
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        controller: _scrollController,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: 3,
                        itemBuilder: (BuildContext context, int idx) {
                          return Container(
                            width: _index == idx ? 16 : 8.0,
                            height: 8.0,
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(14),
                              gradient: LinearGradient(
                                colors:
                                _index != idx
                                    ? [
                                  const Color(0xffC4C4C4),
                                  const Color(0xffC4C4C4),
                                ]
                                    : [
                                  const Color(0xffDC282D),
                                  const Color(0xffF36341),
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 18),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: VButtonGradient(
                    title: 'OK',
                    decoration:
                    _index != 2
                        ? BoxDecoration(
                      color: const Color(0xffC4C4C4),
                      borderRadius: BorderRadius.circular(14),
                    )
                        : null,
                    onTap: () {
                      if (_index == 2) {
                        customNav(
                          context,
                          RouterName.dashboardScreen,
                          isReplace: true,
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget page(DefaultModel item) => Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      const SizedBox(height: 108),
      Image.asset(item.image, width: 254, height: 254),
      const SizedBox(height: 32),
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Text(
          textAlign: TextAlign.center,
          item.title,
          style: Theme.of(context).textTheme.titleLarge,
        ),
      ),
      const SizedBox(height: 24),
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Text(
          textAlign: TextAlign.center,
          context.loc.questionnaireCongrat1Desc,
          style: Theme.of(context).textTheme.titleSmall,
        ),
      ),
      const SizedBox(height: 40),
    ],
  );
}
