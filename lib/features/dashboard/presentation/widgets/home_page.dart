import 'package:flutter/material.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/community_card.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/dashboard_header.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/learning_materials_section.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/dashboard_user_lesson.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const DecoratedBox(
      decoration: BoxDecoration(color: Colors.white),
      child: Safe<PERSON><PERSON>(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Add const constructors to prevent rebuilds
              DashboardHeader(),
              <PERSON><PERSON><PERSON>ox(height: 40,),
              CommunityCard(),
              DashboardUserLesson(),
              LearningMaterialsSection(),
            ],
          ),
        ),
      ),
    );
  }
}
