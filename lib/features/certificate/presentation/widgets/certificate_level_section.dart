import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate_level.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import '../providers/certificate_navigation_provider.dart';

class CertificateLevelSection extends ConsumerWidget {
  final CertificateLevel level;
  final List<Certificate> certificates;

  const CertificateLevelSection({
    super.key,
    required this.level,
    required this.certificates,
  });

  // Solid, vibrant colors per level
  Color _colorForLevel(String name) {
    final n = name.toLowerCase();
    if (n.contains('a1') || n.contains('beginner')) {
      return const Color(0xFF4F46E5); // Indigo 600
    } else if (n.contains('a2')) {
      return const Color(0xFF10B981); // Emerald 500
    } else if (n.contains('b1')) {
      return const Color(0xFF8B5CF6); // Violet 500
    } else if (n.contains('b2')) {
      return const Color(0xFF06B6D4); // Cyan 500
    } else if (n.contains('c1')) {
      return const Color(0xFFF59E0B); // Amber 500
    } else if (n.contains('c2') || n.contains('advanced')) {
      return const Color(0xFFEF4444); // Red 500
    }
    return const Color(0xFF2563EB); // Default: Blue 600
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final latestCertificate =
        certificates.isNotEmpty ? certificates.first : null;
    final formattedDate =
        latestCertificate != null
            ? DateFormat('d MMMM yyyy').format(latestCertificate.dateIssued)
            : '—';

    final baseColor = _colorForLevel(level.name);
    final shadowColor = baseColor.withValues(alpha: .35);

    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            blurRadius: 18,
            spreadRadius: 1,
            offset: const Offset(0, 10),
          ),
        ],
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Material(
        color: baseColor, // Solid card color
        borderRadius: BorderRadius.circular(16.0),
        child: InkWell(
          borderRadius: BorderRadius.circular(16.0),
          onTap: () {
            // Set the certificate data in the provider
            ref
                .read(certificateNavigationControllerProvider.notifier)
                .setCertificateData(level: level, certificates: certificates);

            // Navigate using the navigation helper
            customNav(
              context,
              RouterName.certificateScreen,
              params: {'level': level.name},
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Left: Text and chips
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.military_tech,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              level.name,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w800,
                                fontSize: 18,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          _InfoChip(
                            icon: Icons.workspace_premium_outlined,
                            // e.g. "3 Certificates"
                            label:
                                '${certificates.length} ${context.loc.certificates}',
                          ),
                          // Show date with icon only (no hardcoded text -> no extra l10n key needed)
                          _InfoChip(icon: Icons.schedule, label: formattedDate),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Right: Thumbnail preview
                _PreviewThumb(
                  imageUrl: latestCertificate?.certificateUrl ?? '',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;

  const _InfoChip({required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: .22),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withValues(alpha: .35)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.white),
          const SizedBox(width: 6),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12.5,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class _PreviewThumb extends StatelessWidget {
  final String imageUrl;

  const _PreviewThumb({required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      height: 84,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: .12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: .5), width: 1),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: CachedNetworkImage(
          imageUrl:
              imageUrl.isNotEmpty
                  ? imageUrl
                  : 'https://via.placeholder.com/240x168.png?text=Certificate',
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          placeholder:
              (context, url) => Container(
                color: Colors.white.withValues(alpha: .08),
                child: const Center(child: LoadingCircle()),
              ),
          errorWidget:
              (context, url, error) => Container(
                color: Colors.white.withValues(alpha: .08),
                child: const Center(
                  child: Icon(Icons.description, size: 40, color: Colors.white),
                ),
              ),
        ),
      ),
    );
  }
}
