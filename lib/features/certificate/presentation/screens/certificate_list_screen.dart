import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart' as provider;
import 'package:selfeng/features/certificate/domain/providers/certificate_provider.dart';
import 'package:selfeng/features/certificate/presentation/controllers/certificate_list_controller.dart';
import 'package:selfeng/features/setting/presentation/widgets/congratulatory_widget.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/features/certificate/presentation/widgets/certificate_level_section.dart';

class CertificateListScreen extends ConsumerWidget {
  const CertificateListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final certificateRepository = ref.watch(certificateRepositoryProvider);

    return provider.ChangeNotifierProvider(
      create: (_) => CertificateListController(certificateRepository),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.black54),
            onPressed: () => context.pop(),
          ),
          title: Text(
            context.loc.certificate_list,
            style: const TextStyle(
              color: Colors.black87,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
        ),
        body: provider.Consumer<CertificateListController>(
          builder: (context, controller, child) {
            if (controller.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (controller.error != null) {
              return Center(child: Text(controller.error!));
            }

            if (controller.groupedCertificates.isEmpty) {
              return const Center(child: Text("No certificates found."));
            }

            final levels = controller.groupedCertificates.keys.toList();

            return ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: levels.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return const Padding(
                    padding: EdgeInsets.only(bottom: 16.0),
                    child: CongratulatoryWidget(),
                  );
                }
                final level = levels[index - 1];
                final certificates = controller.groupedCertificates[level]!;

                return CertificateLevelSection(
                  level: level,
                  certificates: certificates,
                );
              },
            );
          },
        ),
      ),
    );
  }
}
