import 'dart:io';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';

class CertificateDownloadService {
  final Dio _dio = Dio();

  Future<bool> downloadCertificate({
    required String url,
    required String fileName,
  }) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final savePath = '${dir.path}/$fileName';

      await _dio.download(url, savePath);

      // You can add logic here to open the file or show a notification
      print('File saved to $savePath');

      return true;
    } catch (e) {
      print('Download failed: $e');
      return false;
    }
  }
}
