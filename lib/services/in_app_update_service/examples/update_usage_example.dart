import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/update_dialog.dart';

/// Example screen showing how to use the in-app update service
class UpdateExampleScreen extends ConsumerWidget {
  const UpdateExampleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    return Scaffold(
      appBar: AppBar(title: const Text('In-App Update Example')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Update Banner (automatically shows when update is available)
            const UpdateBanner(),
            const SizedBox(height: 20),

            // Update Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Update Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildStatusRow(
                      'Update Available',
                      updateState.isUpdateAvailable,
                    ),
                    _buildStatusRow(
                      'High Priority',
                      updateState.isHighPriority,
                    ),
                    _buildStatusRow(
                      'Downloaded',
                      updateState.isUpdateDownloaded,
                    ),
                    _buildStatusRow('Loading', updateState.isLoading),
                    if (updateState.error != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Error: ${updateState.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                    if (updateState.isUpdateAvailable) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Priority: ${updateController.updatePriorityText}',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Action Buttons
            const Text(
              'Manual Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            ElevatedButton(
              onPressed:
                  updateState.isLoading
                      ? null
                      : () async {
                        await updateController.checkForUpdates();
                      },
              child:
                  updateState.isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Check for Updates'),
            ),
            const SizedBox(height: 8),

            if (updateState.isUpdateAvailable &&
                !updateState.isUpdateDownloaded) ...[
              ElevatedButton(
                onPressed:
                    updateState.isLoading
                        ? null
                        : () async {
                          await updateController.startFlexibleUpdate();
                        },
                child: const Text('Start Flexible Update'),
              ),
              const SizedBox(height: 8),

              if (updateController.shouldForceImmediateUpdate) ...[
                ElevatedButton(
                  onPressed:
                      updateState.isLoading
                          ? null
                          : () async {
                            await updateController.startImmediateUpdate();
                          },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Start Immediate Update'),
                ),
                const SizedBox(height: 8),
              ],
            ],

            if (updateState.isUpdateDownloaded) ...[
              ElevatedButton(
                onPressed:
                    updateState.isLoading
                        ? null
                        : () async {
                          await updateController.completeFlexibleUpdate();
                        },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Install Downloaded Update'),
              ),
              const SizedBox(height: 8),
            ],

            OutlinedButton(
              onPressed: () {
                showUpdateDialog(
                  context,
                  isForced: updateController.shouldForceImmediateUpdate,
                );
              },
              child: const Text('Show Update Dialog'),
            ),

            const SizedBox(height: 20),

            // Information Card
            Card(
              color: Colors.blue.withOpacity(0.1),
              child: const Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'How it works',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• The app automatically checks for updates on startup\n'
                      '• Critical updates (version difference > 5) force immediate installation\n'
                      '• Normal updates (version difference ≤ 5) offer flexible installation\n'
                      '• Flexible updates download in background\n'
                      '• Users can install when convenient',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.grey,
            size: 20,
          ),
        ],
      ),
    );
  }
}

/// Example of how to integrate update checking into a custom widget
class CustomUpdateChecker extends ConsumerWidget {
  const CustomUpdateChecker({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    // Listen for update state changes
    ref.listen<InAppUpdateState>(inAppUpdateControllerProvider, (
      previous,
      next,
    ) {
      // Show dialog when critical update becomes available
      if (next.isUpdateAvailable &&
          next.isHighPriority &&
          updateController.shouldForceImmediateUpdate) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showUpdateDialog(context, isForced: true);
        });
      }

      // Show snackbar when flexible update is downloaded
      if (next.isUpdateDownloaded && !(previous?.isUpdateDownloaded ?? false)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Update downloaded and ready to install'),
            action: SnackBarAction(
              label: 'Install',
              onPressed: () {
                updateController.completeFlexibleUpdate();
              },
            ),
          ),
        );
      }
    });

    return const SizedBox.shrink(); // This widget doesn't render anything
  }
}
