import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/flexible_update_dialog.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/forced_update_dialog.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/update_dialog.dart';

/// Example screen demonstrating the new update dialogs
class UpdateDialogsExampleScreen extends ConsumerWidget {
  const UpdateDialogsExampleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    return Scaffold(
      appBar: AppBar(title: const Text('Update Dialogs Example')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Update Status',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Update Available: ${updateState.isUpdateAvailable}'),
                    Text(
                      'Update Downloaded: ${updateState.isUpdateDownloaded}',
                    ),
                    Text('High Priority: ${updateState.isHighPriority}'),
                    Text(
                      'Critical Update: ${updateController.isCriticalUpdate}',
                    ),
                    if (updateState.error != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Error: ${updateState.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Control buttons
            Text(
              'Dialog Examples',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Flexible update dialog
            ElevatedButton.icon(
              onPressed: () => showFlexibleUpdateDialog(context),
              icon: const Icon(Icons.play_arrow),
              label: const Text('Show Flexible Update Dialog'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 12),

            // Forced update dialog
            ElevatedButton.icon(
              onPressed: () => showForcedUpdateDialog(context),
              icon: const Icon(Icons.warning),
              label: const Text('Show Forced Update Dialog'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 12),

            // Auto-select dialog (recommended)
            ElevatedButton.icon(
              onPressed:
                  () => showUpdateDialog(
                    context,
                    isForced: updateController.shouldForceImmediateUpdate,
                  ),
              icon: const Icon(Icons.auto_awesome),
              label: const Text('Show Auto-Selected Dialog'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 24),

            // Service controls
            Text(
              'Service Controls',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed:
                  updateState.isLoading
                      ? null
                      : () => updateController.initialize(),
              child:
                  updateState.isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Initialize Update Service'),
            ),

            const SizedBox(height: 8),

            ElevatedButton(
              onPressed:
                  updateState.isLoading
                      ? null
                      : () => updateController.checkForUpdates(),
              child: const Text('Check for Updates'),
            ),

            const Spacer(),

            // Usage instructions
            Card(
              color: Colors.blue.withOpacity(0.1),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.info, color: Colors.blue),
                        const SizedBox(width: 8),
                        Text(
                          'Usage Guide',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '• Flexible Update Dialog: Use for normal updates with Google Play styling\n'
                      '• Forced Update Dialog: Use for critical updates with full-screen UI\n'
                      '• Auto-Selected Dialog: Automatically chooses the right dialog based on update type',
                      style: TextStyle(height: 1.4),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Example of how to integrate update dialogs in your app
class UpdateDialogIntegrationExample {
  /// Show update dialog based on update availability and priority
  static Future<void> showAppropriateUpdateDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);
    final updateState = ref.read(inAppUpdateControllerProvider);

    if (!updateState.isUpdateAvailable) {
      return;
    }

    // Determine if this should be a forced update
    final shouldForce = updateController.shouldForceImmediateUpdate;

    // Show the appropriate dialog
    await showUpdateDialog(context, isForced: shouldForce);
  }

  /// Example of checking for updates on app start
  static Future<void> checkForUpdatesOnAppStart(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    // Initialize and check for updates
    await updateController.initialize();

    // Show dialog if update is available
    if (context.mounted) {
      await showAppropriateUpdateDialog(context, ref);
    }
  }

  /// Example of showing update banner in dashboard
  static Widget buildUpdateBanner(WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);

    if (!updateState.isUpdateAvailable && !updateState.isUpdateDownloaded) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            updateState.isHighPriority
                ? Colors.orange.withOpacity(0.1)
                : Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: updateState.isHighPriority ? Colors.orange : Colors.blue,
        ),
      ),
      child: Row(
        children: [
          Icon(
            updateState.isUpdateDownloaded
                ? Icons.download_done
                : Icons.system_update,
            color: updateState.isHighPriority ? Colors.orange : Colors.blue,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  updateState.isUpdateDownloaded
                      ? 'Update Ready'
                      : 'Update Available',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  updateState.isUpdateDownloaded
                      ? 'Tap to install the downloaded update'
                      : 'A new version is available',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () {
              // This would be called from a widget with access to context
              // showUpdateDialog(context, isForced: updateController.shouldForceImmediateUpdate);
            },
            child: Text(updateState.isUpdateDownloaded ? 'Install' : 'Update'),
          ),
        ],
      ),
    );
  }
}
