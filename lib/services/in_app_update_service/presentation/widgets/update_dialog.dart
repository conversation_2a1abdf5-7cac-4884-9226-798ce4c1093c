import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/flexible_update_dialog.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/forced_update_dialog.dart';

/// Show update dialog - automatically chooses the appropriate dialog type
Future<void> showUpdateDialog(
  BuildContext context, {
  bool isForced = false,
}) async {
  if (isForced) {
    return showForcedUpdateDialog(context);
  } else {
    return showFlexibleUpdateDialog(context);
  }
}

/// Widget to show update notification banner
class UpdateBanner extends ConsumerWidget {
  const UpdateBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);

    if (!updateState.isUpdateAvailable && !updateState.isUpdateDownloaded) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            updateState.isUpdateDownloaded
                ? Colors.green.withValues(alpha: 0.1)
                : updateState.isHighPriority
                ? Colors.orange.withValues(alpha: 0.1)
                : Colors.blue.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color:
                updateState.isUpdateDownloaded
                    ? Colors.green
                    : updateState.isHighPriority
                    ? Colors.orange
                    : Colors.blue,
            width: 2,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            updateState.isUpdateDownloaded
                ? Icons.download_done
                : Icons.system_update,
            color:
                updateState.isUpdateDownloaded
                    ? Colors.green
                    : updateState.isHighPriority
                    ? Colors.orange
                    : Colors.blue,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  updateState.isUpdateDownloaded
                      ? 'Update Ready to Install'
                      : 'App Update Available',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color:
                        updateState.isUpdateDownloaded
                            ? Colors.green
                            : updateState.isHighPriority
                            ? Colors.orange
                            : Colors.blue,
                  ),
                ),
                Text(
                  updateState.isUpdateDownloaded
                      ? 'Tap to install the downloaded update'
                      : 'A new version is available',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () async {
              final updateController = ref.read(
                inAppUpdateControllerProvider.notifier,
              );

              if (updateState.isUpdateDownloaded) {
                // If update is already downloaded, complete the flexible update
                await updateController.completeFlexibleUpdate();
              } else {
                // Start native update flow
                if (updateController.shouldForceImmediateUpdate) {
                  // For critical updates, use immediate update (native full-screen dialog)
                  await updateController.startImmediateUpdate();
                } else {
                  // For non-critical updates, use flexible update (native notification)
                  await updateController.startFlexibleUpdate();
                }
              }
            },
            child: Text(
              updateState.isUpdateDownloaded ? 'Install' : 'Update',
              style: TextStyle(
                color:
                    updateState.isUpdateDownloaded
                        ? Colors.green
                        : updateState.isHighPriority
                        ? Colors.orange
                        : Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
