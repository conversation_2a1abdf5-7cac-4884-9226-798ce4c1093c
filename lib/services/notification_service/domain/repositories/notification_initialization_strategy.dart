import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';

/// Strategy for initializing notifications at different app lifecycle stages
class NotificationInitializationStrategy {
  static bool _isInitialized = false;
  static bool _isInitializing = false;

  /// Initialize notifications with minimal blocking operations
  /// This can be called during app startup without blocking the UI
  static void initializeMinimal() {
    if (_isInitialized || _isInitializing) return;
    
    _isInitializing = true;
    debugPrint('🔔 Starting minimal notification initialization...');
    
    // Run minimal initialization in background
    Future.microtask(() async {
      try {
        final container = ProviderContainer();
        try {
          final notificationService = container.read(notificationServiceProvider);
          
          // Only do non-blocking initialization here
          await _initializeMinimalNotifications(notificationService);
          
          debugPrint('✅ Minimal notification initialization completed');
        } finally {
          container.dispose();
        }
      } catch (e) {
        debugPrint('❌ Minimal notification initialization failed: $e');
      } finally {
        _isInitializing = false;
      }
    });
  }

  /// Initialize full notifications after app is ready and user might be authenticated
  /// This should be called after the splash screen or when user interaction is possible
  static void initializeFull(WidgetRef ref) {
    if (_isInitialized) return;
    
    debugPrint('🔔 Starting full notification initialization...');
    
    // Run full initialization in background
    Future.microtask(() async {
      try {
        final notificationService = ref.read(notificationServiceProvider);
        final result = await notificationService.initialize();

        result.fold(
          (error) {
            debugPrint('❌ Full notification initialization failed: ${error.message}');
          },
          (_) {
            debugPrint('✅ Full notification initialization completed');
            _isInitialized = true;
          },
        );
      } catch (e) {
        debugPrint('❌ Full notification initialization exception: $e');
      }
    });
  }

  /// Initialize notifications after user authentication
  /// This ensures FCM tokens can be saved properly
  static void initializeAfterAuth(WidgetRef ref) {
    debugPrint('🔔 Starting post-authentication notification setup...');
    
    Future.microtask(() async {
      try {
        final notificationService = ref.read(notificationServiceProvider);
        
        // Refresh FCM token after authentication
        final refreshResult = await notificationService.refreshFCMToken();
        refreshResult.fold(
          (error) => debugPrint('❌ FCM token refresh failed: ${error.message}'),
          (_) => debugPrint('✅ FCM token refreshed after authentication'),
        );
        
        // Ensure full initialization is complete
        if (!_isInitialized) {
          initializeFull(ref);
        }
      } catch (e) {
        debugPrint('❌ Post-auth notification setup failed: $e');
      }
    });
  }

  /// Minimal notification setup that doesn't require user interaction or authentication
  static Future<void> _initializeMinimalNotifications(dynamic notificationService) async {
    try {
      // Only setup background message handler and basic configuration
      // Avoid permission requests, FCM token operations, and topic subscriptions
      
      // This would need to be implemented in the notification service
      // For now, we'll skip the heavy operations
      debugPrint('📱 Minimal notification setup completed');
    } catch (e) {
      debugPrint('❌ Minimal notification setup failed: $e');
    }
  }

  /// Check if notifications are fully initialized
  static bool get isInitialized => _isInitialized;

  /// Check if notifications are currently initializing
  static bool get isInitializing => _isInitializing;

  /// Reset initialization state (useful for testing)
  static void reset() {
    _isInitialized = false;
    _isInitializing = false;
  }
}
