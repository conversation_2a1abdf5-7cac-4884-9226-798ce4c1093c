import 'package:flutter/material.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/sound_wave_audio_player.dart';

/// Demo screen showing the enhanced SoundWaveAudioPlayer
/// 
/// This demonstrates:
/// 1. Real audio duration extraction from remote URLs
/// 2. Consistent waveform generation based on audio URL
/// 3. Proper playback controls and progress tracking
class SoundWaveDemoScreen extends StatelessWidget {
  const SoundWaveDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sound Wave Audio Player Demo'),
        backgroundColor: const Color(0xffE21F29),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Audio Player Features:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Real audio duration from remote files\n'
              '• Consistent waveform visualization\n'
              '• Progress tracking with visual feedback\n'
              '• Smooth animations and transitions',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 32),
            
            // Example 1: Sample audio file
            const Text(
              'Sample Audio 1:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const SoundWaveAudioPlayer(
                audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
                isEnabled: true,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Example 2: Different audio file to show different waveform
            const Text(
              'Sample Audio 2:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const SoundWaveAudioPlayer(
                audioUrl: 'https://www.soundjay.com/misc/sounds/fail-buzzer-02.wav',
                isEnabled: true,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Example 3: Disabled player
            const Text(
              'Disabled Player:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const SoundWaveAudioPlayer(
                audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
                isEnabled: false,
              ),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              'Technical Details:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Duration is extracted from actual audio files using audioplayers package\n'
              '• Waveform is generated consistently based on URL hash for visual consistency\n'
              '• Each unique URL produces a unique but consistent waveform pattern\n'
              '• Fallback mechanisms ensure the player works even if audio loading fails',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
